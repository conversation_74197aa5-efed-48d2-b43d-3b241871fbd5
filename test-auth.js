/**
 * Authentication Flow Test Script
 * 
 * Tests the complete authentication flow including:
 * - Health check
 * - User registration
 * - User login
 * - Session management
 * - Password reset
 */

const BASE_URL = 'http://localhost:8787';

// Test user data
const testUser = {
  email: '<EMAIL>',
  password: 'TestPassword123!',
  name: 'Test User'
};

/**
 * Make HTTP request with proper error handling
 */
async function makeRequest(url, options = {}) {
  try {
    console.log(`\n🔄 ${options.method || 'GET'} ${url}`);
    
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    });

    const responseText = await response.text();
    let data;
    
    try {
      data = JSON.parse(responseText);
    } catch (e) {
      data = { raw: responseText };
    }

    console.log(`📊 Status: ${response.status} ${response.statusText}`);
    console.log(`📄 Response:`, JSON.stringify(data, null, 2));
    
    return { response, data };
  } catch (error) {
    console.error(`❌ Request failed:`, error.message);
    return { error };
  }
}

/**
 * Test authentication health check
 */
async function testAuthHealth() {
  console.log('\n🏥 Testing Authentication Health Check...');
  
  const { response, data, error } = await makeRequest(`${BASE_URL}/api/auth/health`);
  
  if (error) {
    console.error('❌ Health check failed:', error);
    return false;
  }
  
  if (response.ok && data.healthy) {
    console.log('✅ Authentication service is healthy');
    return true;
  } else {
    console.error('❌ Authentication service is not healthy');
    return false;
  }
}

/**
 * Test user registration
 */
async function testUserRegistration() {
  console.log('\n📝 Testing User Registration...');
  
  const { response, data, error } = await makeRequest(`${BASE_URL}/api/auth/sign-up/email`, {
    method: 'POST',
    body: JSON.stringify({
      email: testUser.email,
      password: testUser.password,
      name: testUser.name
    })
  });
  
  if (error) {
    console.error('❌ Registration request failed:', error);
    return false;
  }
  
  if (response.ok) {
    console.log('✅ User registration successful');
    return true;
  } else {
    console.error('❌ User registration failed');
    return false;
  }
}

/**
 * Test user login
 */
async function testUserLogin() {
  console.log('\n🔐 Testing User Login...');
  
  const { response, data, error } = await makeRequest(`${BASE_URL}/api/auth/sign-in/email`, {
    method: 'POST',
    body: JSON.stringify({
      email: testUser.email,
      password: testUser.password
    })
  });
  
  if (error) {
    console.error('❌ Login request failed:', error);
    return { success: false };
  }
  
  if (response.ok) {
    console.log('✅ User login successful');
    
    // Extract session cookie if present
    const setCookieHeader = response.headers.get('set-cookie');
    let sessionCookie = null;
    
    if (setCookieHeader) {
      const cookieMatch = setCookieHeader.match(/better-auth\.session_token=([^;]+)/);
      if (cookieMatch) {
        sessionCookie = `better-auth.session_token=${cookieMatch[1]}`;
        console.log('🍪 Session cookie extracted');
      }
    }
    
    return { success: true, sessionCookie, data };
  } else {
    console.error('❌ User login failed');
    return { success: false };
  }
}

/**
 * Test session validation
 */
async function testSessionValidation(sessionCookie) {
  console.log('\n🔍 Testing Session Validation...');
  
  if (!sessionCookie) {
    console.log('⚠️ No session cookie available, skipping session validation');
    return false;
  }
  
  const { response, data, error } = await makeRequest(`${BASE_URL}/api/auth/session`, {
    method: 'GET',
    headers: {
      'Cookie': sessionCookie
    }
  });
  
  if (error) {
    console.error('❌ Session validation request failed:', error);
    return false;
  }
  
  if (response.ok && data.user) {
    console.log('✅ Session validation successful');
    console.log(`👤 Logged in as: ${data.user.name} (${data.user.email})`);
    return true;
  } else {
    console.error('❌ Session validation failed');
    return false;
  }
}

/**
 * Test user profile endpoint
 */
async function testUserProfile(sessionCookie) {
  console.log('\n👤 Testing User Profile Endpoint...');
  
  if (!sessionCookie) {
    console.log('⚠️ No session cookie available, skipping profile test');
    return false;
  }
  
  const { response, data, error } = await makeRequest(`${BASE_URL}/api/auth/me`, {
    method: 'GET',
    headers: {
      'Cookie': sessionCookie
    }
  });
  
  if (error) {
    console.error('❌ Profile request failed:', error);
    return false;
  }
  
  if (response.ok && data.status === 'success') {
    console.log('✅ User profile retrieved successfully');
    console.log(`📋 Profile data:`, JSON.stringify(data.data, null, 2));
    return true;
  } else {
    console.error('❌ User profile retrieval failed');
    return false;
  }
}

/**
 * Test user logout
 */
async function testUserLogout(sessionCookie) {
  console.log('\n🚪 Testing User Logout...');
  
  if (!sessionCookie) {
    console.log('⚠️ No session cookie available, skipping logout test');
    return false;
  }
  
  const { response, data, error } = await makeRequest(`${BASE_URL}/api/auth/sign-out`, {
    method: 'POST',
    headers: {
      'Cookie': sessionCookie
    }
  });
  
  if (error) {
    console.error('❌ Logout request failed:', error);
    return false;
  }
  
  if (response.ok) {
    console.log('✅ User logout successful');
    return true;
  } else {
    console.error('❌ User logout failed');
    return false;
  }
}

/**
 * Main test runner
 */
async function runAuthenticationTests() {
  console.log('🚀 Starting Authentication Flow Tests...');
  console.log('=' .repeat(50));
  
  const results = {
    healthCheck: false,
    registration: false,
    login: false,
    sessionValidation: false,
    userProfile: false,
    logout: false
  };
  
  // Test 1: Health Check
  results.healthCheck = await testAuthHealth();
  
  if (!results.healthCheck) {
    console.log('\n❌ Authentication service is not healthy. Stopping tests.');
    return results;
  }
  
  // Test 2: User Registration
  results.registration = await testUserRegistration();
  
  // Test 3: User Login
  const loginResult = await testUserLogin();
  results.login = loginResult.success;
  const sessionCookie = loginResult.sessionCookie;
  
  // Test 4: Session Validation
  if (results.login) {
    results.sessionValidation = await testSessionValidation(sessionCookie);
  }
  
  // Test 5: User Profile
  if (results.login) {
    results.userProfile = await testUserProfile(sessionCookie);
  }
  
  // Test 6: User Logout
  if (results.login) {
    results.logout = await testUserLogout(sessionCookie);
  }
  
  // Summary
  console.log('\n' + '=' .repeat(50));
  console.log('📊 Authentication Test Results:');
  console.log('=' .repeat(50));
  
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${test.replace(/([A-Z])/g, ' $1').toLowerCase()}`);
  });
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All authentication tests passed!');
  } else {
    console.log('⚠️ Some authentication tests failed. Check the logs above for details.');
  }
  
  return results;
}

// Run the tests
runAuthenticationTests().catch(console.error);
