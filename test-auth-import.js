/**
 * Test script to check if auth module can be imported and initialized
 */

async function testAuthImport() {
  try {
    console.log('Testing auth module import...');
    
    // Test basic server response first
    const healthResponse = await fetch('http://localhost:8787/');
    const healthData = await healthResponse.json();
    console.log('✅ Server is responding:', healthData.status);
    
    // Test if auth routes are mounted
    console.log('\nTesting auth route mounting...');
    
    // Try different auth endpoints
    const endpoints = [
      '/api/auth/health',
      '/api/auth/session',
      '/api/auth',
    ];
    
    for (const endpoint of endpoints) {
      try {
        console.log(`\nTesting ${endpoint}...`);
        const response = await fetch(`http://localhost:8787${endpoint}`);
        console.log(`Status: ${response.status} ${response.statusText}`);
        
        const text = await response.text();
        if (text) {
          try {
            const data = JSON.parse(text);
            console.log('Response:', JSON.stringify(data, null, 2));
          } catch (e) {
            console.log('Raw response:', text);
          }
        } else {
          console.log('Empty response');
        }
      } catch (error) {
        console.error(`Error testing ${endpoint}:`, error.message);
      }
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

testAuthImport();
